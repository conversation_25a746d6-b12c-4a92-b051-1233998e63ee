import {ActivityIndicator, View} from 'react-native';
import HotProductsRow from '../HotProductsRow';
import {useNavigation} from '@react-navigation/native';
import {productAction} from '../../../redux/actions/productAction';
import {useEffect, useState} from 'react';
import {ColorThemes} from '../../../assets/skin/colors';
import {Product} from '../../../redux/models/product';
import {navigate, RootScreen} from '../../../router/router';
import {getRandomObjects} from '../../../utils/arrayUtils';

const FreeShipProductSection = ({onRefresh}: {onRefresh: boolean}) => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);

  useEffect(() => {
    initData();
  }, [onRefresh]);

  const initData = async () => {
    try {
      setLoading(true);
      let data = await productAction.find({
        page: 1,
        size: 100,
        query: '@IsFreeShip:true',
        noGetImage: true,
      });
      if (data.length > 10) {
        data = getRandomObjects(data, 10);
      }
      const products = data.map((item: any) => ({
        Id: item.Id,
        Name: item.Name,
        Price: item.Price,
        Img: item.Img,
        rating: item.Rating,
        soldCount: item.SoldCount,
        Description: item.Description,
      }));
      setProducts(products);
    } finally {
      setLoading(false);
    }
  };

  const onSeeAll = () => {
    navigation.navigate(RootScreen.ProductListByCategory as never);
  };

  const onProductPress = (product: Product) => {
    navigate(RootScreen.ProductDetail, {id: product.Id});
  };

  if (loading) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <ActivityIndicator
          size="large"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }

  return (
    <HotProductsRow
      title="Free Ship"
      products={products}
      onSeeAll={onSeeAll}
      showRating={false}
      onProductPress={onProductPress}
    />
  );
};

export default FreeShipProductSection;
