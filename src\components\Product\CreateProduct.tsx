import React, {useEffect, useRef, useState, useMemo, useCallback} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import ImageCropPicker, {ImageOrVideo} from 'react-native-image-crop-picker';
import {useNavigation} from '@react-navigation/native';
import DescriptionImage from '../Field/DescriptionImage';
import {Controller, useForm} from 'react-hook-form';
import {
  AppButton,
  AppSvg,
  ComponentStatus,
  FBottomSheet,
  showBottomSheet,
  showSnackbar,
  Checkbox,
} from 'wini-mobile-components';
import {useDispatch, useSelector} from 'react-redux';
import {ColorThemes} from '../../assets/skin/colors';
import {DataController} from '../../base/baseController';
import {randomGID, Ultis} from '../../utils/Utils';
import {useSelectorShopState} from '../../redux/hook/shopHook ';
import {ProductActions} from '../../redux/reducers/ProductReducer';
import ConfigAPI from '../../Config/ConfigAPI';
import {BaseDA} from '../../base/BaseDA';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faAngleRight} from '@fortawesome/free-solid-svg-icons';
import iconSvg from '../../svg/icon';
import {fetchCategories} from '../../redux/actions/categoryAction';
import {CreateProductProps, ProductFormData, ProductState} from '../dto/dto';
import {BottomSheetCreatProduct} from '../BottomSheet/BottomSheetCreatProduct';

const CreateProduct = ({routeParams}: CreateProductProps) => {
  console.log('check-CreateProduct');
  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();
  const popupRef = useRef<any>(null);
  const popupRef2 = useRef<any>(null);

  // Controllers
  const ProductController = useMemo(() => new DataController('Product'), []);
  const BrandController = useMemo(() => new DataController('Brand'), []);
  const CategoryController = useMemo(() => new DataController('Category'), []);
  // Selectors
  const shopInfo = useSelectorShopState().data;
  // State management
  const [productState, setProductState] = useState<ProductState>({
    image: undefined,
    avataProduct: '',
    listProduct: '',
    label: '',
    lengText: {
      ProductName: 0,
      DesProduct: 0,
    },
  });
  // Form setup
  const {
    control,
    handleSubmit,
    setValue,
    formState: {errors},
    watch,
  } = useForm<ProductFormData>({
    defaultValues: {
      image: null,
      ProductName: '',
      DesProduct: '',
      productType: '',
      label: '',
      listImage: '',
      price: '',
      InStock: '',
      freeship: false,
    },
  });
  const watchedValues = {
    ProductName: watch('ProductName'),
    DesProduct: watch('DesProduct'),
    image: watch('image'),
    productType: watch('productType'),
    label: watch('label'),
    price: watch('price'),
    InStock: watch('InStock'),
    freeship: watch('freeship'),
  };
  // Hàm lấy ảnh khi chỉnh sửa hoặc sao chép
  const getImageEdit = useCallback(async (Img: string, ImgList: string) => {
    try {
      const url = ConfigAPI.url.split('/api/')[0];
      let renderImage: any[] = [];
      let avatarUrl = '';

      // Handle single image (avatar)
      if (Img && Img.trim()) {
        try {
          const imageAvata = await BaseDA.getFilesInfor([Img]);
          if (
            imageAvata?.data &&
            Array.isArray(imageAvata.data) &&
            imageAvata.data.length > 0 &&
            imageAvata.data[0]?.Url
          ) {
            avatarUrl = `${url}${imageAvata.data[0].Url}`;
          }
        } catch (error) {
          console.error('Error fetching avatar image:', error);
          // Continue execution even if avatar fails
        }
      }

      // Handle image list
      if (ImgList && ImgList.trim()) {
        try {
          const imageIds = ImgList.split(',').filter(id => id.trim());
          if (imageIds.length > 0) {
            const imageList = await BaseDA.getFilesInfor(imageIds);
            if (imageList?.data && Array.isArray(imageList.data)) {
              renderImage = imageList.data
                .filter(
                  (item: any) =>
                    item && item.Url && typeof item.Url === 'string',
                )
                .map((item: any) => ({
                  path: `${url}${item.Url}`,
                  mime: item.MimeType || 'image/jpeg',
                  filename: item.FileName || 'image.jpg',
                }));
            }
          }
        } catch (error) {
          console.error('Error fetching image list:', error);
          // Continue execution even if image list fails
        }
      }

      // Update state once with all the data
      setProductState(prev => ({
        ...prev,
        image: renderImage,
        avataProduct:
          avatarUrl || (renderImage.length > 0 ? renderImage[0].path : ''),
      }));
    } catch (error) {
      console.error('Error in getImageEdit:', error);
      // Set default state in case of complete failure
      setProductState(prev => ({
        ...prev,
        image: [],
        avataProduct: '',
      }));
    }
  }, []);
  // hàm xoá ảnh
  const deleteImage = (index: number) => {
    console.log('check-index', index);

    if (
      productState.image &&
      Array.isArray(productState.image) &&
      productState.image.length > index
    ) {
      const newImages = [...productState.image];
      newImages.splice(index, 1);
      setProductState(prev => ({
        ...prev,
        image: newImages,
        avataProduct: newImages.length > 0 ? newImages[0].path : '',
      }));
    }
  };
  // hàm lấy danh mục và nhãn
  const getTypeProductAndLabelEdit = useCallback(
    async (labelID: string, BrandId: string) => {
      try {
        const promises = [];

        // Only make API calls if the IDs exist
        if (labelID) {
          promises.push(
            CategoryController.getListSimple({query: `@Id: {${labelID}}`}),
          );
        } else {
          promises.push(Promise.resolve(null));
        }

        if (BrandId) {
          promises.push(
            BrandController.getListSimple({query: `@Id: {${BrandId}}`}),
          );
        } else {
          promises.push(Promise.resolve(null));
        }

        const [responeCategory, responeBrandId] = await Promise.all(promises);

        if (
          responeCategory?.code === 200 &&
          responeCategory.data &&
          responeCategory.data.length > 0
        ) {
          setProductState(prev => ({
            ...prev,
            listProduct: responeCategory.data[0]?.Name || '',
          }));
        }

        if (
          responeBrandId?.code === 200 &&
          responeBrandId.data &&
          responeBrandId.data.length > 0
        ) {
          setProductState(prev => ({
            ...prev,
            label: responeBrandId.data[0]?.Name || '',
          }));
        }
      } catch (error) {
        console.error('Error in getTypeProductAndLabelEdit:', error);
        // Don't crash, just log the error
      }
    },
    [CategoryController, BrandController],
  );
  // hàm chọn ảnh
  const pickerImg = useCallback(async () => {
    try {
      const img = await ImageCropPicker.openPicker({
        multiple: true,
        cropping: true,
        maxFiles: 5,
      });
      if (img.length > 5) {
        showSnackbar({
          message: 'Bạn chỉ được chọn tối đa 5 ảnh',
          status: ComponentStatus.ERROR,
        });
        return;
      }
      if (img) {
        console.log('check-img', img);
        setProductState(prev => ({
          ...prev,
          image: img,
          avataProduct: img[0].path,
        }));
        let arrayImg = [];
        for (let i = 0; i < img.length; i++) {
          arrayImg.push({
            uri: img[i].path,
            type: img[i].mime,
            name: img[i].filename ?? 'file',
          });
        }
        const resImgs = await BaseDA.uploadFiles(arrayImg);
        if (resImgs.length > 0) {
          setValue('image', resImgs[0].Id);
          setValue('listImage', resImgs.map((img: any) => img.Id).toString());
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi chọn ảnh',
        status: ComponentStatus.ERROR,
      });
    }
  }, [setValue]);
  // hàm format tiền
  const formatCurrency = useCallback((value: string) => {
    if (!value) return '';
    // Loại bỏ tất cả ký tự không phải số
    const numericValue = value.replace(/[^0-9]/g, '');
    if (!numericValue) return '';
    // Format với dấu phẩy
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }, []);
  // hàm format tiền
  const handlePriceChange = useCallback(
    (text: string, onChange: (value: string) => void) => {
      // Loại bỏ tất cả ký tự không phải số từ input
      const numericValue = text.replace(/[^0-9]/g, '');
      // Lưu giá trị số thuần vào form
      onChange(numericValue);
    },
    [],
  );
  useEffect(() => {
    if (routeParams?.dataEdit) {
      const {dataEdit} = routeParams;
      console.log('check-dataEdit', dataEdit);
      setValue('image', dataEdit.Img || '');
      setValue('ProductName', dataEdit.Name || '');
      setValue('DesProduct', dataEdit.Description || '');
      setValue('productType', dataEdit.CategoryId || '');
      setValue('label', dataEdit.BrandId || '');
      // Fix: Handle ListImg safely and convert to string for form
      const listImgArray = dataEdit.ListImg
        ? dataEdit.ListImg.split(',').filter((img: string) => img.trim())
        : [];
      setValue('listImage', listImgArray.join(','));
      setValue('price', dataEdit.Price?.toString() || '');
      setValue('InStock', dataEdit.InStock?.toString() || '');
      setValue('freeship', dataEdit.IsFreeShip || false);
      if (dataEdit.Img || dataEdit.ListImg) {
        getImageEdit(dataEdit.Img || '', dataEdit.ListImg || '');
      }
      if (dataEdit.CategoryId || dataEdit.BrandId) {
        getTypeProductAndLabelEdit(
          dataEdit.CategoryId || '',
          dataEdit.BrandId || '',
        );
      }
    }
    if (routeParams?.title === 'Copy sản phẩm' && routeParams?.dataCopy) {
      const {dataCopy} = routeParams;
      try {
        setValue('image', dataCopy.Img || '');
        setValue('ProductName', dataCopy.Name || '');
        setValue('DesProduct', dataCopy.Description || '');
        setValue('productType', dataCopy.CategoryId || '');
        setValue('label', dataCopy.BrandId || '');
        // Fix: Handle ListImg safely and convert to string for form
        const listImgArray = dataCopy.ListImg
          ? dataCopy.ListImg.split(',').filter((img: string) => img.trim())
          : [];
        setValue('listImage', listImgArray.join(','));
        setValue('price', dataCopy.Price?.toString() || '');
        setValue('InStock', dataCopy.InStock?.toString() || '');
        setValue('freeship', dataCopy.IsFreeShip || false);
        if (dataCopy.Img || dataCopy.ListImg) {
          getImageEdit(dataCopy.Img || '', dataCopy.ListImg || '');
        }
        if (dataCopy.CategoryId || dataCopy.BrandId) {
          getTypeProductAndLabelEdit(
            dataCopy.CategoryId || '',
            dataCopy.BrandId || '',
          );
        }
      } catch (error) {
        console.error('Error processing copy data:', error);
        showSnackbar({
          message: 'Có lỗi khi sao chép dữ liệu sản phẩm',
          status: ComponentStatus.ERROR,
        });
      }
    }
  }, [routeParams, setValue, getImageEdit, getTypeProductAndLabelEdit]);

  useEffect(() => {
    dispatch(fetchCategories());
  }, []);
  useEffect(() => {
    setProductState(prev => ({
      ...prev,
      lengText: {
        ProductName: watchedValues.ProductName?.length || 0,
        DesProduct: watchedValues.DesProduct?.length || 0,
      },
    }));
  }, [watchedValues.ProductName, watchedValues.DesProduct]);

  const onSubmit = useCallback(
    async (data: ProductFormData) => {
      try {
        if (!shopInfo || shopInfo.length === 0) {
          showSnackbar({
            message: 'Thông tin shop không tồn tại',
            status: ComponentStatus.ERROR,
          });
          return;
        }

        const productData = {
          Id: routeParams?.dataEdit ? routeParams?.dataEdit?.Id : randomGID(),
          DateCreated: routeParams?.dataEdit
            ? routeParams?.dataEdit?.DateCreated
            : Date.now(),
          Name: data.ProductName,
          Description: data.DesProduct,
          Content: data.DesProduct,
          Img: data.image,
          Price: Number(data.price),
          Status: 1,
          // Content: productState.listProduct,
          ListImg: data.listImage.toString(),
          InStock: data.InStock ? Number(data.InStock) : 0,
          CategoryId: data.productType,
          BrandId: data.label,
          ShopId: shopInfo[0]?.Id,
          IsFreeShip: data.freeship,
        };

        if (routeParams?.title === 'Chỉnh sửa sản phẩm') {
          const response = await ProductController.edit([productData]);
          console.log('check-edit-response:', response);
          if (response.code === 200) {
            showSnackbar({
              message: 'Cập nhật sản phẩm thành công',
              status: ComponentStatus.SUCCSESS,
            });
            dispatch(ProductActions.getInforProduct(shopInfo[0].Id));
            navigation.goBack();
          }
        } else {
          const response = await ProductController.add([productData]);
          console.log('check-add-response:', response);
          if (response.code === 200) {
            showSnackbar({
              message: 'Tạo sản phẩm mới thành công',
              status: ComponentStatus.SUCCSESS,
            });
            dispatch(ProductActions.getInforProduct(shopInfo[0].Id));
            navigation.goBack();
          }
        }
      } catch (error) {
        console.error('Error submitting product:', error);
        console.error('Error details:', {
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : 'No stack trace',
          data: (error as any)?.response?.data,
        });
        showSnackbar({
          message: 'Có lỗi xảy ra khi lưu sản phẩm',
          status: ComponentStatus.ERROR,
        });
      }
    },
    [
      routeParams,
      productState.listProduct,
      shopInfo,
      ProductController,
      dispatch,
      navigation,
    ],
  );
  return (
    <KeyboardAvoidingView
      style={{flex: 1, position: 'relative'}}
      behavior="padding"
      enabled>
      <ScrollView>
        <View style={styles.container}>
          <FBottomSheet ref={popupRef} />
          <FBottomSheet ref={popupRef2} />
          <Controller
            control={control}
            name="image"
            rules={{
              required: 'Vui lòng chọn ảnh',
            }}
            render={({field: {value, onChange}}) => (
              <DescriptionImage
                deleteImage={deleteImage}
                checkImage={productState.image}
                image={productState.image as ImageOrVideo[]}
                pickerImg={pickerImg}
                avataProduct={productState.avataProduct}
                imageListEdit={productState.image as ImageOrVideo[]}
              />
            )}
          />
          {errors.image && watch('image') == null && (
            <Text style={{color: 'red', marginLeft: 10, fontFamily: 'roboto'}}>
              {errors.image.message}
            </Text>
          )}

          <View style={styles.section}>
            <View
              style={{flexDirection: 'row', justifyContent: 'space-between'}}>
              <Text style={styles.label}>Tên sản phẩm *</Text>
              <Text style={styles.limit}>
                {productState.lengText.ProductName}/50
              </Text>
            </View>
            <Controller
              control={control}
              name="ProductName"
              rules={{
                required: 'Vui lòng nhập tên sản phẩm',
              }}
              render={({field: {value, onChange}}) => (
                <TextInput
                  style={{height: 40}}
                  placeholder="Nhập tên sản phẩm"
                  placeholderTextColor="#DDDDDD"
                  maxLength={50}
                  value={value}
                  onChange={e => onChange(e.nativeEvent.text)}
                />
              )}
            />
          </View>
          {errors.ProductName && watch('ProductName') == '' && (
            <Text style={{color: 'red', marginLeft: 10, fontFamily: 'roboto'}}>
              {errors.ProductName.message}
            </Text>
          )}

          <View style={styles.section}>
            <View
              style={{flexDirection: 'row', justifyContent: 'space-between'}}>
              <Text style={styles.label}>Mô tả sản phẩm *</Text>
              <Text style={styles.limit}>
                {productState.lengText.DesProduct}/3000
              </Text>
            </View>
            <Controller
              control={control}
              name="DesProduct"
              rules={{
                required: 'Vui lòng nhập mô tả sản phẩm',
              }}
              render={({field: {value, onChange}}) => (
                <TextInput
                  style={{height: 40, backgroundColor: 'white'}}
                  placeholder="Mô tả sản phẩm"
                  placeholderTextColor="#DDDDDD"
                  value={value}
                  onChange={e => onChange(e.nativeEvent.text)}
                  multiline={true}
                />
              )}
            />
          </View>
          {errors.DesProduct && watch('DesProduct') == '' && (
            <Text style={{color: 'red', marginLeft: 10, fontFamily: 'roboto'}}>
              {errors.DesProduct.message}
            </Text>
          )}

          <View>
            <View>
              <Controller
                control={control}
                name="productType"
                rules={{
                  required: 'Vui lòng chọn danh mục sản phẩm',
                }}
                render={({field: {value}}) => (
                  <TouchableOpacity
                    style={styles.option}
                    onPress={() => {
                      showBottomSheet({
                        ref: popupRef2,
                        enableDismiss: true,
                        children: (
                          <BottomSheetCreatProduct
                            type={'Product'}
                            ref={popupRef2}
                            handleSelect={(value: any) => {
                              setProductState(prev => ({
                                ...prev,
                                listProduct: value?.name,
                              }));
                              setValue('productType', value?.id);
                            }}
                          />
                        ),
                      });
                    }}>
                    <View
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <AppSvg SvgSrc={iconSvg.listProduct} size={16} />
                      <Text
                        style={{
                          marginLeft: 10,
                          color:
                            productState.listProduct == ''
                              ? '#DDDDDD'
                              : 'black',
                        }}>
                        {' '}
                        {productState.listProduct
                          ? productState.listProduct
                          : 'Danh mục sản phẩm *'}
                      </Text>
                    </View>
                    <Text style={styles.optionPlaceholder}>
                      <FontAwesomeIcon
                        icon={faAngleRight}
                        color={ColorThemes.light.black}
                        size={16}
                      />
                    </Text>
                  </TouchableOpacity>
                )}
              />
              {errors.productType && watch('productType') == '' && (
                <Text
                  style={{color: 'red', marginLeft: 10, fontFamily: 'roboto'}}>
                  {errors.productType.message}
                </Text>
              )}
            </View>
            <View>
              <Controller
                control={control}
                name="label"
                rules={{
                  required: 'Vui lòng chọn Nhãn sản phẩm',
                }}
                render={({field: {value}}) => (
                  <TouchableOpacity
                    style={styles.option}
                    onPress={() => {
                      showBottomSheet({
                        ref: popupRef,
                        enableDismiss: true,
                        children: (
                          <BottomSheetCreatProduct
                            type={'label'}
                            ref={popupRef}
                            handleSelectLabel={(value: any) => {
                              setProductState(prev => ({
                                ...prev,
                                label: value?.name,
                              }));
                              setValue('label', value?.id);
                            }}
                          />
                        ),
                      });
                    }}>
                    <View
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <AppSvg SvgSrc={iconSvg.label} size={16} />
                      <Text
                        style={{
                          marginLeft: 10,
                          color: productState.label ? 'black' : '#DDDDDD',
                        }}>
                        {' '}
                        {productState.label
                          ? productState.label
                          : 'Nhãn hàng *'}
                      </Text>
                    </View>
                    <Text style={styles.optionPlaceholder}>
                      <FontAwesomeIcon
                        icon={faAngleRight}
                        color={ColorThemes.light.black}
                        size={16}
                      />
                    </Text>
                  </TouchableOpacity>
                )}
              />
              {errors.label && watch('label') == '' && (
                <Text
                  style={{color: 'red', marginLeft: 10, fontFamily: 'roboto'}}>
                  {errors.label.message}
                </Text>
              )}
            </View>
            <View style={[styles.option, {position: 'relative'}]}>
              <Controller
                control={control}
                name="price"
                rules={{
                  required: 'Vui lòng chọn giá sản phẩm',
                }}
                render={({field: {value, onChange}}) => (
                  <View
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      flex: 1,
                      paddingRight: 50, // Make space for VND text
                    }}>
                    <AppSvg SvgSrc={iconSvg.price} size={16} />
                    <TextInput
                      style={{height: 40, marginLeft: 8, flex: 1}}
                      placeholder="Nhập giá sản phẩm"
                      keyboardType="numeric"
                      placeholderTextColor="#DDDDDD"
                      value={formatCurrency(value)}
                      onChange={e =>
                        handlePriceChange(e.nativeEvent.text, onChange)
                      }
                    />
                  </View>
                )}
              />
              <View style={styles.vndContainer}>
                <Text style={styles.vndText}>VNĐ</Text>
              </View>
            </View>
            {errors.price && watch('price') == '' && (
              <Text
                style={{color: 'red', marginLeft: 10, fontFamily: 'roboto'}}>
                {errors.price.message}
              </Text>
            )}
            <View style={styles.option}>
              <Controller
                control={control}
                name="InStock"
                rules={{
                  required: 'Vui lòng nhập số tồn kho',
                  validate: value => {
                    if (!value || value.trim() === '') {
                      return 'Vui lòng nhập số tồn kho';
                    }
                    const numValue = Number(value);
                    if (isNaN(numValue) || numValue < 0) {
                      return 'Số tồn kho phải là số và lớn hơn hoặc bằng 0';
                    }
                    return true;
                  },
                }}
                render={({field: {value, onChange}}) => (
                  <View
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <AppSvg SvgSrc={iconSvg.price} size={16} />
                    <TextInput
                      style={{height: 40, marginLeft: 8, width: 350}}
                      placeholder="Tồn kho"
                      placeholderTextColor="#DDDDDD"
                      keyboardType="numeric"
                      value={value || ''}
                      onChange={e => onChange(e.nativeEvent.text)}
                    />
                  </View>
                )}
              />
            </View>
            {errors.InStock && (
              <Text
                style={{
                  color: 'red',
                  marginLeft: 10,
                  fontFamily: 'roboto',
                }}>
                {errors.InStock.message}
              </Text>
            )}
            <View style={{marginLeft: 10, marginTop: 10, marginBottom: 10}}>
              <Controller
                control={control}
                name="freeship"
                render={({field: {value, onChange}}) => (
                  <View
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      width: '100%',
                    }}>
                    <View
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <Checkbox value={value} onChange={onChange} size={24} />
                      <Text
                        style={{
                          marginLeft: 10,
                          color: 'black',
                          fontSize: 14,
                        }}>
                        Free ship
                      </Text>
                    </View>
                  </View>
                )}
              />
            </View>
            <View style={{marginBottom: 100}} />
          </View>
        </View>
        <TouchableOpacity
          style={styles.buyButton}
          onPress={handleSubmit(onSubmit)}>
          <Text style={styles.actionButtonText}>
            {routeParams?.title == 'Chỉnh sửa sản phẩm'
              ? 'Chỉnh sửa'
              : 'Tạo mới'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F0F0F0',
    position: 'relative',
  },
  section: {
    marginTop: 10,
    marginLeft: 10,
    marginRight: 10,
    backgroundColor: 'white',
    borderRadius: 5,
    padding: 10,
    elevation: 2, // Tạo bóng cho Android
    shadowColor: '#A9A9A9', // Tạo bóng cho iOS
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  imageText: {
    color: '#E14337',
    marginLeft: 21,
    fontWeight: 100,
    fontSize: 12,
  },
  label: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#333',
  },
  imagePlaceholder: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderStyle: 'dashed',
    width: 73,
    height: 73,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 5,
    marginTop: 10,
    marginLeft: 10,
  },
  placeholderText: {
    color: '#888',
    fontSize: 14,
  },
  input: {
    borderRadius: 5,
    padding: 10,
    fontSize: 14,
    color: '#555555',
  },
  inputMoney: {
    flex: 1,
    fontSize: 16,
    color: 'black',
    marginLeft: 10,
  },
  multilineInput: {
    height: 50,
    textAlignVertical: 'top',
  },
  limit: {
    fontSize: 12,
    color: '#888',
    textAlign: 'right',
    marginTop: 5,
  },
  option: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    borderRadius: 5,
    marginTop: 10,
    marginRight: 10,
    marginLeft: 10,
    backgroundColor: 'white',
    height: 55,
  },

  optionPlaceholder: {
    fontSize: 14,
    color: '#888',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },

  vndContainer: {
    position: 'absolute',
    right: 10,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
  },

  vndText: {
    color: 'blue',
    fontSize: 14,
    fontWeight: '500',
  },

  optionPlaceholderLastItem: {
    fontSize: 14,
    color: '#888',
    height: 40,
    borderLeftColor: '#00FFFF',
    borderLeftWidth: 0.4,
    minWidth: 60,
  },
  buyButton: {
    backgroundColor: 'blue',
    width: '80%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50,
    borderRadius: 30,
    margin: 'auto',
    position: 'absolute',
    bottom: 40,
    left: '11%',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default CreateProduct;
