import { getDataToAsyncStorage } from '../utils/AsyncStorage';
import { StorageContanst } from '../Config/Contanst';
import SocketService from '../modules/chat/services/SocketService';
import store from '../redux/store/store';
import { disconnectSocket } from '../redux/reducers/ChatReducer';

/**
 * Service để quản lý kết nối socket khi user đăng nhập/đăng xuất
 * Bây giờ chỉ là wrapper cho SocketConnectionManager
 */
class AuthSocketService {
  private isInitialized = false;
  private currentUserId: string | null = null;

  /**
   * Khởi tạo kết nối socket khi user đã đăng nhập
   * Được gọi khi:
   * - App khởi động và có token
   * - User đăng nhập thành công
   * - User đăng nhập bằng social
   */
  async initializeSocketConnection(userId?: string, Name?: string) {
    try {
      console.log('🔌 [AuthSocketService] Delegating to SocketConnectionManager...');

      // Lấy thông tin từ AsyncStorage nếu không được truyền vào
      const finalUserId = userId || await this.getUserId();
      if (!finalUserId) {
        console.log('❌ [AuthSocketService] Missing userId for socket connection');
        return false;
      }

      // Delegate to SocketConnectionManager để tránh duplicate logic
      const SocketConnectionManager = require('../utils/SocketConnectionManager').default;
      const result = await SocketConnectionManager.connect(finalUserId, Name || '');

      if (result) {
        this.isInitialized = true;
        this.currentUserId = finalUserId;
        console.log('✅ [AuthSocketService] Socket connection delegated successfully');
      }

      return result;
    } catch (error) {
      console.error('❌ [AuthSocketService] Failed to delegate socket connection:', error);
      this.isInitialized = false;
      this.currentUserId = null;
      return false;
    }
  }

  /**
   * Ngắt kết nối socket khi user đăng xuất
   */
  disconnectSocket() {
    try {
      console.log('🔌 [AuthSocketService] Disconnecting socket...');

      SocketService.disconnect();
      store.dispatch(disconnectSocket());

      this.isInitialized = false;
      this.currentUserId = null;
      console.log('✅ [AuthSocketService] Socket disconnected successfully');
    } catch (error) {
      console.error('❌ [AuthSocketService] Failed to disconnect socket:', error);
    }
  }

  /**
   * Kiểm tra trạng thái kết nối socket
   */
  isConnected(): boolean {
    return this.isInitialized && SocketService.getConnectionStatus();
  }

  /**
   * Lấy userId từ Redux store hoặc AsyncStorage
   */
  private async getUserId(): Promise<string | null> {
    try {
      // Lấy từ Redux store trước
      const state = store.getState();
      const customer = state.customer?.data;
      
      if (customer?.id || customer?.Id) {
        return customer.id || customer.Id;
      }

      // Fallback: có thể lấy từ AsyncStorage nếu cần
      // const userId = await getDataToAsyncStorage('userId');
      // return userId;

      return null;
    } catch (error) {
      console.error('Error getting userId:', error);
      return null;
    }
  }

  /**
   * Retry kết nối socket nếu bị mất kết nối
   */
  async retryConnection() {
    if (!this.isConnected()) {
      console.log('🔄 Retrying socket connection...');
      const customer = store.getState().customer.data;
      if (customer?.Id) {
        return await this.initializeSocketConnection(customer.Id, customer.Name);
      }else{
        return false;
      }
    }
    return true;
  }
}

export default new AuthSocketService();
