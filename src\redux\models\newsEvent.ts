interface NewsEvent {
  DateCreated: number;
  Id: string;
  Key: string;
  DateEnd: number;
  DateStart: number;
  Code: string;
  Title: string;
  Address: string;
  Img: string;
  Description: string;
  Name: string;
  Content: string;
  Sort: number;
  Status: number;
  Type: number;
  isRegistered?: boolean;
  Likes?: number;
  Comments?: number;
  Views?: number;
  EventLink?: string;
  AddressLink?: string;
}

export type {NewsEvent};
