import {createSlice, PayloadAction, Dispatch} from '@reduxjs/toolkit';
import {useNavigation} from '@react-navigation/native';
import {BaseDA} from '../../base/BaseDA';
import ConfigAPI from '../../Config/ConfigAPI';
import {DataController} from '../../base/baseController';
import {
  clearDataToAsyncStorage,
  getDataToAsyncStorage,
  removeDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../utils/AsyncStorage';
import store, {RootState} from '../store/store';
import {navigateReset, RootScreen} from '../../router/router';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import {getFcmToken} from '../../features/notifications/fcm/fcm_helper';
import {
  CustomerStatus,
  MissionType,
  StatusOrder,
  StorageContanst,
  TransactionStatus,
  TransactionType,
} from '../../Config/Contanst';
import AuthSocketService from '../../services/AuthSocketService';
import {randomGID, Ultis} from '../../utils/Utils';
import ShopDA from '../../modules/shop/da';
import {handleActionsShop, ProductActions} from './ShoptReducer';
import WalletDA from '../../modules/wallet/da';
import {getRankCustomer} from '../actions/customerAction';
import {AchievedRank} from '../models/rank';

interface CustomerSimpleResponse {
  data?: any;
  myAddress?: Array<any>;
  onLoading: boolean;
  type?: string;
  rankInfo?: {
    totalReward: number;
    totalScore: number;
    achievedRank: AchievedRank | null;
    RanksData: Array<any> | null;
  } | null;
  rankInfoLoading: boolean;
}

const initState: CustomerSimpleResponse = {
  data: undefined,
  myAddress: [],
  onLoading: false,
  rankInfo: null,
  rankInfoLoading: false,
};

export const customerSlice = createSlice({
  name: 'customer',
  initialState: initState,
  reducers: {
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case 'GETINFOR':
          state.data = action.payload.data;
          state.rankInfo = action.payload.rankInfo;
          break;
        case 'GETMYADDRESS':
          state.myAddress = action.payload.data;
          break;
        case 'ADDADDRESS':
          if (
            state.myAddress &&
            state.myAddress.length > 0 &&
            state.myAddress.find(el => el.Id === action.payload.data.Id)
          ) {
            state.myAddress = state.myAddress.map(el => {
              if (el.Id === action.payload.data.Id) {
                return action.payload.data;
              }
              return el;
            });
          } else {
            if (state.myAddress && state.myAddress.length == 0) {
              action.payload.data.IsDefault = true;
            }
            state.myAddress = [action.payload.data, ...(state.myAddress || [])];
          }
          if (action.payload.data.IsDefault) {
            state.myAddress = state.myAddress.map(el => {
              if (el.Id === action.payload.data.Id) {
                return action.payload.data;
              }
              return {
                ...el,
                IsDefault: false,
              };
            });
          }
          break;
        case 'DELETEADDRESS':
          if (state.myAddress) {
            // kiem tra địa chỉ là mặc định thì chuyển địa chỉ khác làm mặc định
            const defaultAddress = state.myAddress.find(
              el => el.Id === action.payload.data,
            );

            state.myAddress = state.myAddress.filter(
              el => el.Id !== action.payload.data,
            );
            if (defaultAddress.IsDefault) {
              if (state.myAddress?.length > 0) {
                state.myAddress[0].IsDefault = true;
              }
            }
          }
          break;
        case 'UPDATE':
          state.data = action.payload.data;
          break;
        case 'LOGOUT':
          state.data = undefined;
          break;
        default:
          break;
      }
      state.onLoading = false;
    },
    onFetching: state => {
      state.onLoading = true;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(getRankCustomer.pending, state => {
        state.rankInfoLoading = true;
      })
      .addCase(getRankCustomer.fulfilled, (state, action) => {
        state.rankInfo = action.payload;
        state.rankInfoLoading = false;
      })
      .addCase(getRankCustomer.rejected, state => {
        state.rankInfo = null;
        state.rankInfoLoading = false;
      });
  },
});

const {handleActions, onFetching} = customerSlice.actions;

export default customerSlice.reducer;

export class CustomerActions {
  static login = async (body: {
    type: 'phone' | 'apple' | 'google' | 'microsoft' | 'account';
    token?: string;
    deviceToken?: string;
    ggClientId?: string;
    phone?: string;
    password?: string;
    email?: string;
  }) => {
    const res = await BaseDA.post(ConfigAPI.url + 'data/login', {
      headers: {module: 'Customer', pid: ConfigAPI.pid},
      body: body,
    });
    if (res.code === 200) {
      saveDataToAsyncStorage('accessToken', `${res.data.accessToken}`);
      saveDataToAsyncStorage('refreshToken', `${res.data.refreshToken}`);
      saveDataToAsyncStorage(
        'timeRefresh',
        `${(Date.now() / 1000 + 9 * 60).toString()}`,
      );
    }
    return res;
  };

  static lockAccount = async (phone: string) => {
    const controller = new DataController('Customer');
    const res = (await controller.getListSimple({
      page: 1,
      size: 1,
      query: `@Mobile:{${phone}}`,
    })) as any;
    if (res.data.length) {
      await controller.edit([{...res.data[0], Status: CustomerStatus.locked}]);
      return {
        code: 200,
        message:
          'Tài khoản đã bị khóa, vui lòng liên hệ với quản trị viên để được hỗ trợ.',
      };
    } else return {code: 400, message: 'Tài khoản không tồn tại'};
  };

  static getInfor = (toLogin?: boolean) => async (dispatch: Dispatch) => {
    dispatch(onFetching());
    const controller = new DataController('Customer');
    const rankController = new DataController('ConfigRank');
    const missionController = new DataController('Mission');
    const missionCustomerController = new DataController('MissionCustomer');

    const shopDA = new ShopDA();

    const res = await BaseDA.get(ConfigAPI.url + 'data/getInfo', {
      headers: {module: 'Customer', pid: ConfigAPI.pid},
    });
    if (res.code === 200) {
      await saveDataToAsyncStorage(StorageContanst.CustomerId, res.data.Id);
      const shop = await shopDA.getShop(res.data.Id);
      if (shop.code === 200) {
        dispatch(
          handleActionsShop({
            type: 'GETINFORSHOP',
            data: shop.data,
          }),
        );
      }
      const deviceToken = await getDataToAsyncStorage('fcmToken');
      // EDIT DEVICE TOKEN
      if (!res.data.DeviceToken?.includes(deviceToken) && deviceToken) {
        res.data.DeviceToken ??= '';
        res.data.DeviceToken += `,${deviceToken}`;
        res.data.DeviceToken = res.data.DeviceToken.split(',').filter(
          (devTk: string, i: number, arr: Array<string>) =>
            devTk.length && arr.indexOf(devTk) === i,
        );
        if (res.data.DeviceToken.length > 3) {
          res.data.DeviceToken = res.data.DeviceToken.slice(
            res.data.DeviceToken.length - 3,
          );
        }
        res.data.DeviceToken = res.data.DeviceToken.join(',');
        await controller.edit([res.data]);
      }
      //#region rank
      var rankInfo = store.getState().customer.rankInfo ?? {
        totalReward: 0,
        totalScore: 0,
        achievedRank: null,
        RanksData: [],
      };
      if (!rankInfo.achievedRank) {
        // Gọi tất cả API cùng lúc với Promise.all
        const [totalRewardRes, resSum] = await Promise.all([
          // 1. Tính tổng điểm tất cả
          new DataController('HistoryReward').group({
            reducers:
              'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalReward',
            searchRaw: `@CustomerId: {${res.data.Id}} ((@Status: [${TransactionStatus.success}] @Value: [0 +inf]) | (@Status: [${TransactionStatus.pending} ${TransactionStatus.success}] @Value: [-inf 0]))`,
          }),
          // 2. Tính tổng điểm từ hoa hồng và nhiệm vụ
          new DataController('HistoryReward').group({
            reducers:
              'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalReward',
            searchRaw: `@CustomerId: {${res.data.Id}} (@Type: [${TransactionType.hoahong}] | @Type: [${TransactionType.mission}]) @Status: [${TransactionStatus.success}]`,
          }),
        ]);
        // Xử lý kết quả tổng điểm hiển thị
        if (totalRewardRes.code === 200 && totalRewardRes.data.length > 0) {
          rankInfo.totalReward = totalRewardRes.data[0].TotalReward || 0;
        }
        // Xử lý kết quả điểm để tính rank
        let totalScore = 0;
        if (resSum.code === 200 && resSum.data.length > 0) {
          totalScore = resSum.data[0].TotalReward || 0;
        }

        // 3. Lấy thông tin config rank
        const RankController = new DataController('ConfigRank');
        const resRank = await RankController.getAll();
        // Xử lý rank và xác định rank hiện tại
        if (resRank.code === 200 && resRank.data.length > 0) {
          const ranksData = resRank.data;
          // Sắp xếp ranks theo điểm số tăng dần
          const sortedRanks = [...ranksData].sort(
            (a, b) => parseFloat(a.Score) - parseFloat(b.Score),
          );
          rankInfo.RanksData = sortedRanks;
          // Tìm rank hiện tại dựa trên điều kiện

          for (const rank of sortedRanks) {
            const requiredScore = parseFloat(rank.Score);
            // Kiểm tra điều kiện điểm số
            if (totalScore >= requiredScore) {
              rankInfo.achievedRank = rank;
            }
          }
        }
      }
      //#endregion
      // for mission login
      if (toLogin && res.data?.Id) {
        const walletda = new WalletDA();
        // #region xử lý nhiệm vụ
        await walletda.CaculateMisson(
          res.data?.Id,
          MissionType.Login,
          rankInfo,
        );
      }
      dispatch(
        handleActions({
          type: 'GETINFOR',
          data: res.data,
          rankInfo: rankInfo,
        }),
      );
    }
  };

  static checkPassword = async (phone: string, password?: string) => {
    const res = await BaseDA.post(ConfigAPI.url + 'data/checkPassword', {
      headers: {module: 'Customer', pid: ConfigAPI.pid},
      body: {
        ...(phone.includes('@') ? {email: phone} : {phone: phone}),
        password: password,
      },
    });
    return res;
  };

  static hashPassword = async (password?: string) => {
    const res = await BaseDA.get(
      ConfigAPI.url + `data/bcrypt?password=${password}`,
      {
        headers: {module: 'Customer', pid: ConfigAPI.pid},
      },
    );
    return res;
  };

  static logout = () => async (dispatch: Dispatch) => {
    const deviceToken = await getDataToAsyncStorage('fcmToken');
    // Get customer data from parameters instead of store
    const customerController = new DataController('Customer');
    const user = store.getState().customer.data;
    if (!user) {
      navigateReset(RootScreen.login);
      dispatch(
        handleActions({
          type: 'LOGOUT',
        }),
      );
      return;
    }

    if (deviceToken && user?.DeviceToken?.includes(deviceToken)) {
      customerController.edit([
        {
          ...user,
          DeviceToken: user?.DeviceToken
            ? user.DeviceToken?.split(',')
                .filter((e: any) => e.length && e !== deviceToken)
                .join(',')
            : undefined,
        },
      ]);
    }
    // const res = await customerController.edit([
    //   {...user, DeviceToken: undefined},
    // ]);
    // if (res.code == 200) {

    // Ngắt kết nối socket trước khi clear data
    AuthSocketService.disconnectSocket();

    await removeDataToAsyncStorage('accessToken');
    await removeDataToAsyncStorage('refreshToken');
    await removeDataToAsyncStorage('timeRefresh');
    await removeDataToAsyncStorage('timeRefresh');
    await removeDataToAsyncStorage(StorageContanst.CartItems);
    await removeDataToAsyncStorage(StorageContanst.CustomerId);
    await removeDataToAsyncStorage(StorageContanst.ShopId);
    navigateReset(RootScreen.login);
    // NotificationActions.reset(store.dispatch);
    dispatch(
      handleActions({
        type: 'LOGOUT',
      }),
    );
    // }
  };

  static edit = (user: any) => async (dispatch: Dispatch) => {
    dispatch(onFetching());
    const controller = new DataController('Customer');
    const res = await controller.edit([user]);
    if (res.code === 200) {
      showSnackbar({
        message: 'Cập nhật thông tin tài khoản thành công',
        status: ComponentStatus.SUCCSESS,
      });
      dispatch(
        handleActions({
          type: 'UPDATE',
          data: user,
        }),
      );
    }
    return res;
  };

  static getAddresses = (cusId: any) => async (dispatch: Dispatch) => {
    if (!cusId) return;
    dispatch(onFetching());
    const controller = new DataController('Address');
    const res = await controller.getListSimple({
      page: 1,
      size: 10,
      query: `@CustomerId: {${cusId}}`,
    });
    if (res.code === 200) {
      dispatch(
        handleActions({
          type: 'GETMYADDRESS',
          data: res.data,
        }),
      );
    }
    return res;
  };

  static editAddress =
    (address: any, isAdd?: boolean) => async (dispatch: Dispatch) => {
      dispatch(onFetching());
      const controller = new DataController('Address');
      if (address?.IsDefault) {
        const resCheck = await controller.getListSimple({
          page: 1,
          size: 1,
          query: `@CustomerId: {${address.CustomerId}} @IsDefault: {true}`,
        });
        if (resCheck.code === 200) {
          if (resCheck.data.length) {
            await controller.edit([{...resCheck.data[0], IsDefault: false}]);
          }
        }
      }
      const res = isAdd
        ? await controller.add([address])
        : await controller.edit([address]);
      if (res.code === 200) {
        showSnackbar({
          message: 'Thao tác thành công',
          status: ComponentStatus.SUCCSESS,
        });
        dispatch(
          handleActions({
            type: 'ADDADDRESS',
            data: address,
          }),
        );
      }
      return res;
    };

  static deleteAddress = (addressId: any) => async (dispatch: Dispatch) => {
    const controller = new DataController('Address');
    const res = await controller.delete([addressId]);
    if (res.code === 200) {
      showSnackbar({
        message: 'Thao tác thành công',
        status: ComponentStatus.SUCCSESS,
      });
      dispatch(
        handleActions({
          type: 'DELETEADDRESS',
          data: addressId,
        }),
      );
    }
    return res;
  };

  static delete = async (
    dispatch: Dispatch,
    userId: string,
    navigation: any,
  ) => {
    dispatch(onFetching());
    const controller = new DataController('Customer');
    const res = await controller.delete([userId]);
    if (res.code === 200) {
      clearDataToAsyncStorage();
      getFcmToken();
      showSnackbar({
        message:
          'Tài khoản đã bị xóa khỏi hệ thống, vui lòng đăng nhập lại để sử dụng',
        status: ComponentStatus.WARNING,
      });
      navigation.reset({
        index: 0,
        routes: [{name: RootScreen.login}],
      });
    }
  };
  //tạo action cho việc cập nhật rank và lưu vào lịch sử
  static updateRank =
    (rank: number, gameId: string) => async (dispatch: Dispatch) => {
      const controller = new DataController('Customer');
      const customer = store.getState().customer;
      const res = await controller.edit([
        {...customer?.data, Rank: (customer?.data?.Rank ?? 0) + rank},
      ]);
      if (res.code === 200) {
        // lưu vào bảng HistoryGame
        const historyController = new DataController('HistoryScore');
        const data = {
          Id: randomGID(),
          CustomerId: customer?.data.Id,
          GameId: gameId,
          Score: rank,
          Name: customer?.data?.Name,
          DateCreated: new Date().getTime(),
        };
        await historyController.add([data]);
        dispatch(
          handleActions({
            type: 'UPDATE',
            data: {...customer.data, Rank: (customer?.data?.Rank ?? 0) + rank},
          }),
        );
      }
    };
  static setUp2FA = async (id: string) => {
    const res = await BaseDA.post(ConfigAPI.url + '2fa/setup', {
      headers: {module: 'Customer', pid: ConfigAPI.pid},
      body: {Id: id},
    });
    return res;
  };
  static verify2FA = async (id: string, token: string) => {
    const res = await BaseDA.post(ConfigAPI.url + '2fa/verify', {
      headers: {module: 'Customer', pid: ConfigAPI.pid},
      body: {Id: id, token: token},
    });
    return res;
  };
  static disable2FA = async (id: string) => {
    const res = await BaseDA.post(ConfigAPI.url + '2fa/disable', {
      headers: {module: 'Customer', pid: ConfigAPI.pid},
      body: {Id: id},
    });
    return res;
  };
  static verify2Action = async (id: string, token: string) => {
    const res = await BaseDA.post(ConfigAPI.url + '2fa/protected-action', {
      headers: {module: 'Customer', pid: ConfigAPI.pid},
      body: {Id: id, token: token},
    });
    return res;
  };
  static createWallet = async (user: string) => {
    const res = await BaseDA.post(ConfigAPI.urlBlockchain + 'auth/login', {
      headers: {module: 'Customer', pid: ConfigAPI.pid},
      body: {
        username: ConfigAPI.username_blc,
        password: ConfigAPI.password_blc,
      },
    });
    if (res) {
      // create wallet
      const resCreate = await BaseDA.post(
        ConfigAPI.urlBlockchain + 'wallet/create',
        {
          headers: {Authorization: `Bearer ${res.token}`},
          body: {
            name: `Chainivo Wallet ${user}`,
            description: 'Wallet for Chainivo',
          },
        },
      );
      return resCreate;
    } else {
      return res;
    }
  };
  //checkEmail exist
  static checkEmail = async (email: string) => {
    const controller = new DataController('Customer');
    const res = await controller.getListSimple({
      page: 1,
      size: 1,
      query: `@Email:("${email}") @Id: {${store.getState().customer.data?.Id}}`,
    });
    return res;
  };
  //sendMail
  static sendMail = async (email: string) => {
    const res = await BaseDA.post(ConfigAPI.url + '2fa/send-mail', {
      headers: {module: 'Customer', pid: ConfigAPI.pid},
      body: {email: email},
    });
    return res;
  };
  //verifyMail
  static verifyMail = async (email: string, token: string) => {
    const res = await BaseDA.post(ConfigAPI.url + '2fa/verify-otp', {
      headers: {module: 'Customer', pid: ConfigAPI.pid},
      body: {email: email, otp: token},
    });
    return res;
  };
  static updateDeviceToken = async (deviceToken: string) => {
    const controller = new DataController('Customer');
    const res = await controller.edit([
      {
        Id: store.getState().customer.data?.Id,
        DeviceToken: deviceToken,
      },
    ]);
    return res;
  };
}
