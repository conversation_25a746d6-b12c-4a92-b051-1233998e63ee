import {DataController} from '../../base/baseController';

export class ProductDA {
  private ShopController: DataController;
  constructor() {
    this.ShopController = new DataController('Product');
  }

  async getProducts(ShopId: string, Status?: number) {
    const response = await this.ShopController.aggregateList({
      searchRaw: `@ShopId:{${ShopId}} @Status: [${Status}]`,
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async getAllProducts(ShopId: string) {
    const response = await this.ShopController.aggregateList({
      searchRaw: `@ShopId:{${ShopId}}`,
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async updateValueDiscount(data: any) {
    const response = await this.ShopController.edit(data);
    if (response?.code === 200) {
      return response;
    }
    return null;
  }
  async getInforProductPromotion(ShopId: string) {
    const response = await this.ShopController.getListSimple({
      query: `@ShopId:{${ShopId}}`,
    });
    if (response?.code === 200) {
      console.log('check-response?.data', response?.data);
      if (response?.data && response?.data?.length > 0) {
        let filterData = response?.data?.filter((item: any) => item?.Discount);
        return filterData;
      }
    }
  }
}

export default ProductDA;
