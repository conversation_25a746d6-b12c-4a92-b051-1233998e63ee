import React, {useState, useRef, useEffect} from 'react';
import {View, StyleSheet, Dimensions, SafeAreaView} from 'react-native';
import PagerView from 'react-native-pager-view';
import {useNavigation, useRoute} from '@react-navigation/native';
import {
  showSnackbar,
  ComponentStatus,
  FBottomSheet,
  showBottomSheet,
  FLoading,
} from 'wini-mobile-components';
import TitleWithBackAction from '../../Layout/titleWithBackAction';
import StepIndicator from '../../../components/StepIndicator';
import Step1TransferInfo from '../TransferCANPoint/Step1TransferInfo';
import Step2OTPVerification from '../TransferCANPoint/Step2OTPVerification';
import Step3TransactionDetail from '../TransferCANPoint/Step3TransactionDetail';
import CustomerBottomSheet from '../../../components/CustomerBottomSheet';
import {ColorThemes} from '../../../assets/skin/colors';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import HeaderShop from '../../../components/shop/HeaderShop';

import {DataController} from '../../../base/baseController';
import {randomGID, Ultis} from '../../../utils/Utils';
import {TransactionStatus, TransactionType} from '../../../Config/Contanst';
import {InforHeader} from '../../Layout/headers/inforHeader';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';
import Step1TransferInfoMoney from './Step1TransferInfoMoney';
import Step2OTPVerificationMoney from './Step2OTPVerificationMoney';
import Step3TransactionDetailMoney from './Step3TransactionDetailMoney';
import {useSelectorShopState} from '../../../redux/hook/shopHook ';
import ShopDA from '../../../modules/shop/da';

const {width} = Dimensions.get('window');

const WIthDrawMoney: React.FC = () => {
  const navigation = useNavigation();
  const pagerRef = useRef<PagerView>(null);
  const customerBottomSheetRef = useRef<any>(null);
  const customer = useSelectorCustomerState().data;
  //router param
  const route = useRoute<any>();
  const type = route?.params?.Type;
  // State management
  const [currentStep, setCurrentStep] = useState(1);
  const [transferAmount, setTransferAmount] = useState('');
  const [recipientName, setRecipientName] = useState('');
  const [getDataStepOne, setGetDataStepOne] = useState<any>(null);

  const [recipientPhone, setRecipientPhone] = useState('');
  const [recipientId, setRecipientId] = useState('');
  const [otpValue, setOtpValue] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [transactionData, setTransactionData] = useState<any>(null);

  // Mock current points - replace with actual data
  const [currentPoints, setCurrentPoints] = useState(0);
  const [loading, setLoading] = useState(false);
  const shopInfo = useSelectorShopState().data;
  let shopDA = new ShopDA();
  let [shopMoney, setShopMoney] = useState<any>(null);

  const getShopMoney = async () => {
    await shopDA.getShopFinancialSummary(shopInfo[0]?.Id).then(res => {
      if (res?.code === 200) {
        console.log('check-res', res?.data?.balance);
        setShopMoney(res?.data?.balance);
      }
    });
  };
  useEffect(() => {
    getShopMoney();
  }, []);

  useEffect(() => {
    console.log('check-getDataStepOne', getDataStepOne);
  }, [getDataStepOne]);

  const handleBack = () => {
    if (currentStep > 1) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      pagerRef.current?.setPage(prevStep - 1);
    } else {
      navigation.goBack();
    }
  };

  const handleStep1Next = () => {
    setCurrentStep(2);
    pagerRef.current?.setPage(1);

    // TODO: call API send otp
  };

  const handleOTPComplete = (otp: string) => {
    setOtpValue(otp);
  };

  const handleResendOTP = () => {
    setOtpValue('');
    showSnackbar({
      message: 'Đã gửi lại mã OTP',
      status: ComponentStatus.SUCCSESS,
    });
  };

  const handleVerifyOTP = async () => {
    console.log('check-otpValue', otpValue);
    setIsVerifying(true);
    // TODO: call API verify otp
    const result = await CustomerActions.verify2Action(customer.Id, otpValue);
    console.log('check-result', result);
    if (result.code !== 200) {
      setIsVerifying(false);
      showSnackbar({
        message: 'Mã xác thực không chính xác',
        status: ComponentStatus.ERROR,
      });
      return;
    }
    // Mock OTP verification
    //TODO: call API verify otp
    var lstData = [];
    var dataSend = {
      Id: randomGID(),
      CustomerId: customer?.Id,
      Name: customer?.Name,
      TotalPrice: parseInt(getDataStepOne?.amount),
      NumberAccount: getDataStepOne?.accountNumber,
      BankName: getDataStepOne?.bankName,
      CustomerName: getDataStepOne?.recipientName,
      Status: TransactionStatus.pending,
      ShopId: shopInfo[0]?.Id,
    };
    lstData.push(dataSend);
    console.log('check-lstData', lstData);
    const controller = new DataController('PaymentWithdraw');
    const res = await controller.add(lstData);
    if (res.code === 200) {
      setIsVerifying(false);
      var transactionData = {
        status:
          dataSend.Status === TransactionStatus.success
            ? 'success'
            : dataSend.Status === TransactionStatus.pending
            ? 'pending'
            : 'failed',
        transactionId: dataSend?.Id,
        amount: parseInt(getDataStepOne?.amount),
        customerName: customer?.Name || '',
        customerPhone: customer?.Mobile || '',
        bankAccount: getDataStepOne?.accountNumber || '',
        senderName: customer?.Name || '',
        recipientName: getDataStepOne?.recipientName || '',
        timestamp: new Date().toISOString(),
        type: type,
      };
      setTransactionData(transactionData);
      setCurrentStep(3);
      pagerRef.current?.setPage(2);
      showSnackbar({
        message: type === TransactionType.tranfer ? 'Rút tiền thành công!' : '',
        status: ComponentStatus.SUCCSESS,
      });
    } else {
      setIsVerifying(false);
      showSnackbar({
        message: type === TransactionType.tranfer ? 'Rút tiền thất bại!' : '',
        status: ComponentStatus.ERROR,
      });
    }
  };

  const handleDone = () => {
    navigation.goBack();
  };

  useEffect(() => {
    if (!customer?.IsEnable2FA) {
      showSnackbar({
        message: 'Vui lòng Bật xác thực 2 lớp để thực hiện giao dịch',
        status: ComponentStatus.ERROR,
      });
      return;
    }
  }, [customer]);

  return (
    <View style={styles.container}>
      <InforHeader title="Rút tiền" />
      {loading && <FLoading visible={loading} />}
      <View style={styles.container}>
        {/* phần header */}
        <FBottomSheet ref={customerBottomSheetRef} />
        <StepIndicator currentStep={currentStep} totalSteps={3} />

        <PagerView
          ref={pagerRef}
          style={styles.pagerView}
          initialPage={0}
          scrollEnabled={false}>
          <View key="step1" style={styles.pageContainer}>
            <Step1TransferInfoMoney
              currentPoints={shopMoney}
              transferAmount={transferAmount}
              onNext={handleStep1Next}
              type={type}
              setGetDataStepOne={setGetDataStepOne}
              getDataStepOne={{getDataStepOne}}
            />
          </View>

          <View key="step2" style={styles.pageContainer}>
            <Step2OTPVerificationMoney
              phoneNumber={'0782358556'}
              isVerifying={isVerifying}
              onOTPComplete={handleOTPComplete}
              onResendOTP={handleResendOTP}
              onVerify={handleVerifyOTP}
              otpValue={otpValue}
            />
          </View>

          <View key="step3" style={styles.pageContainer}>
            {transactionData && (
              <Step3TransactionDetailMoney
                transactionData={transactionData}
                onDone={handleDone}
              />
            )}
          </View>
        </PagerView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  pagerView: {
    flex: 1,
  },
  pageContainer: {
    flex: 1,
    width: width,
  },
});

export default WIthDrawMoney;
